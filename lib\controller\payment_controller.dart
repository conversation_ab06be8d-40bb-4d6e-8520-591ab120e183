import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:get/get.dart';
import 'package:smsautoforwardapp/bnb.dart';
import 'package:smsautoforwardapp/controller/auth_controller.dart';
import 'package:smsautoforwardapp/model/subscription_model.dart';
import 'package:smsautoforwardapp/shared/utils/api_service.dart';
import 'package:http/http.dart' as http;
import 'package:smsautoforwardapp/style.dart';

// +++++++++++++++++++++++++++++++++++
// ++ STRIPE PAYMENT INITIALIZATION ++
// +++++++++++++++++++++++++++++++++++

class PaymentController extends GetxController {
  final AuthController authController = Get.find();

  Rx<bool> isCreatingSubscription = false.obs;
  Rx<bool> isModifyingSubscription = false.obs;
  Rx<bool> isCancellingSubscription = false.obs;

  Future<void> init({
    required String amount,
    required String planID,
    required String planDescription,
    required String subscriptionPlan,
    String? tempUserId, // Add tempUserId parameter
    String? existingStripeCustomerID, // Renamed to avoid conflict
    String? tempUserEmail, // Add email parameter for upgrade flow
    String? tempUserName, // Add name parameter for upgrade flow
  }) async {
    try {
      final userId = authController.user?.uid;
      final userEmail = authController.user?.email;
      final userName = authController.user?.displayName;

      if (userId == null || userEmail == null) {
        print(
            'Flutter: Regular user auth failed, checking tempUserId parameter...');
        // Check if we're in upgrade flow with temp user ID
        if (tempUserId != null) {
          print(
              'Flutter: Found tempUserId parameter, proceeding with upgrade flow');
          print('Flutter: tempUserId: $tempUserId');
          print('Flutter: existingStripeCustomerID: $existingStripeCustomerID');
          // We can't fetch from Firestore here because user is signed out
          // We need to get user data from the parameters passed from login
          // For now, we'll use a simplified approach - the email and name should be passed as parameters
          // But since we're in upgrade flow, we'll proceed with the stripe customer ID we have
          await _processPaymentWithUserDataForUpgrade(
            userId: tempUserId,
            stripeCustomerID: existingStripeCustomerID,
            amount: amount,
            planID: planID,
            planDescription: planDescription,
            subscriptionPlan: subscriptionPlan,
            userEmail: tempUserEmail,
            userName: tempUserName,
          );
          return;
        }
        print(
            'Flutter: No valid user data found - cannot proceed with payment');

        getErrorSnackBar('User not logged in properly.');
        return;
      }

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      String? stripeCustomerID;

      // Fetch existing customer ID from Firestore
      if (userDoc.exists) {
        stripeCustomerID =
            userDoc.data()?['stripeCustomerID']?.toString() ?? '';
      }

      Map<String, dynamic> customer;

      if (stripeCustomerID == null || stripeCustomerID.isEmpty) {
        // No customer ID stored, try to find by email in Stripe first
        customer = await findCustomerByEmail(userEmail);

        if (customer.isEmpty) {
          // If not found, create new one
          customer = await createCustomer(email: userEmail, name: userName);
        }

        // Save customer ID in Firestore
        await authController.updateStripeCustomerId(id: customer['id']);
      } else {
        // Try fetching by ID from Stripe
        try {
          customer = await getStripeCustomer(stripeCustomerID);
        } catch (e) {
          // If customer not found by ID, fall back to email
          customer = await findCustomerByEmail(userEmail);

          if (customer.isEmpty) {
            customer = await createCustomer(email: userEmail, name: userName);
          }

          // Update stored ID in Firestore
          await authController.updateStripeCustomerId(id: customer['id']);
        }
      }

      await createSubscriptionWithCustomer(
        customer['id'],
        planID,
        amount,
        planDescription,
        subscriptionPlan,
        tempUserId: tempUserId,
        newStripeCustomerID:
            customer['id'], // Pass the customer ID for updating Firestore later
      );
    } catch (e) {
      print('error:$e');
      if (e is StripeException && e.error.code == FailureCode.Canceled) {
        return;
      }

      getErrorSnackBar('Something went wrong, Please try again');
    }
  }

  // Helper method to process payment for upgrade flow (user is signed out, can't access Firestore)
  Future<void> _processPaymentWithUserDataForUpgrade({
    required String userId,
    required String? stripeCustomerID,
    required String amount,
    required String planID,
    required String planDescription,
    required String subscriptionPlan,
    String?
        userEmail, // Add email parameter for creating new customer if needed
    String? userName, // Add name parameter for creating new customer if needed
  }) async {
    Map<String, dynamic> customer;
    print('userEmail:$userEmail');
    print('userName:$userName');
    if (stripeCustomerID == null || stripeCustomerID.isEmpty) {
      // No customer ID available, create a new customer if we have email
      if (userEmail == null || userEmail.isEmpty) {
        print(
            'Flutter: No stripe customer ID and no email available for upgrade flow');
        throw Exception(
            'No stripe customer ID and no email available for upgrade flow');
      }
      print('Flutter: Creating new customer with email: $userEmail');
      customer = await createCustomer(email: userEmail, name: userName);
    } else {
      // Try fetching by ID from Stripe
      try {
        print(
            'Flutter: Attempting to fetch customer from Stripe: $stripeCustomerID');
        customer = await getStripeCustomer(stripeCustomerID);

        // Check if the response contains an error (like the one you're seeing)
        if (customer.containsKey('error')) {
          print(
              'Flutter: Customer not found in Stripe, error: ${customer['error']}');
          // Customer doesn't exist, create a new one if we have email
          if (userEmail == null || userEmail.isEmpty) {
            throw Exception(
                'Stripe customer not found and no email available for creating new customer');
          }
          print('Flutter: Creating new customer with email: $userEmail');
          customer = await createCustomer(email: userEmail, name: userName);
        } else {
          print('Flutter: Successfully fetched customer from Stripe');
          print('Flutter: Customer data: $customer');
        }
      } catch (e) {
        // If customer not found by ID, create a new one if we have email
        print('Flutter: Error fetching customer from Stripe: $e');
        if (userEmail == null || userEmail.isEmpty) {
          throw Exception(
              'Stripe customer not found and no email available for creating new customer');
        }
        print('Flutter: Creating new customer with email: $userEmail');
        customer = await createCustomer(email: userEmail, name: userName);
      }
    }

    // Validate that customer has a valid ID
    final String? customerId = customer['id']?.toString();
    if (customerId == null || customerId.isEmpty) {
      print('Flutter: Customer ID is null or empty in response: $customer');
      throw Exception('Invalid customer ID received from Stripe');
    }

    await createSubscriptionWithCustomer(
      customerId,
      planID,
      amount,
      planDescription,
      subscriptionPlan,
      tempUserId: userId,
      newStripeCustomerID:
          customerId, // Pass the customer ID for updating Firestore later
    );
  }

  // Helper function to check if the customer exists in Stripe
  Future<Map<String, dynamic>> getStripeCustomer(String customerId) async {
    final customerResponse = await apiService(
      endpoint: 'customers/$customerId',
      requestMethod: ApiServiceMethodType.get,
    );

    if (customerResponse == null) {
      throw Exception('Customer not found in Stripe');
    }

    return customerResponse;
  }

  Future<Map<String, dynamic>> findCustomerByEmail(String email) async {
    final response = await apiService(
      endpoint: 'customers?email=$email',
      requestMethod: ApiServiceMethodType.get,
    );

    if (response != null &&
        response.containsKey('data') &&
        response['data'].isNotEmpty) {
      return response['data'][0];
    }

    return {}; // empty if not found
  }

  // Helper function to handle subscription creation with a valid customer
  Future<void> createSubscriptionWithCustomer(
    String customerId,
    String planID,
    String amount,
    String planDescription,
    String subscriptionPlan, {
    String? tempUserId, // Add tempUserId parameter
    String? newStripeCustomerID, // Add newStripeCustomerID parameter
  }) async {
    try {
      Map<String, dynamic> paymentIntent =
          await createPaymentIntent(customerId);
      await createCreditCard(
          customerId, paymentIntent['client_secret'], amount);

      Map<String, dynamic> customerPaymentMethods =
          await getCustomerPaymentMethods(customerId);

      await createSubscription(
        customerId: customerId,
        planID: planID,
        paymentId: customerPaymentMethods['data'][0]['id'],
        planDescription: planDescription,
        subscriptionPlan: subscriptionPlan,
        tempUserId: tempUserId,
        newStripeCustomerID: newStripeCustomerID,
      );
    } catch (e) {
      print('error createSubscriptionWithCustomer:$e');
    }
  }

  // +++++++++++++++++++++
  // ++ CREATE CUSTOMER ++
  // +++++++++++++++++++++

  Future<Map<String, dynamic>> createCustomer({
    required String email,
    required String? name,
  }) async {
    final customerResponse = await apiService(
      endpoint: 'customers',
      requestMethod: ApiServiceMethodType.post,
      requestBody: {
        'email': email,
        'name': name ?? '',
        'description': 'SMSAutoForwarder app subscription',
      },
    );

    return customerResponse!;
  }

  // ++++++++++++++++++++++++++
  // ++ SETUP PAYMENT INTENT ++
  // ++++++++++++++++++++++++++

  Future<Map<String, dynamic>> createPaymentIntent(String customerId) async {
    final paymentIntentCreationResponse = await apiService(
      requestMethod: ApiServiceMethodType.post,
      endpoint: 'setup_intents',
      requestBody: {
        'customer': customerId,
        'automatic_payment_methods[enabled]': 'true',
      },
    );

    return paymentIntentCreationResponse!;
  }

  // ++++++++++++++++++++++++
  // ++ CREATE CREDIT CARD ++
  // ++++++++++++++++++++++++

  Future<void> createCreditCard(
    String customerId,
    String paymentIntentClientSecret,
    String amount,
  ) async {
    await Stripe.instance.initPaymentSheet(
      paymentSheetParameters: SetupPaymentSheetParameters(
        primaryButtonLabel: 'Subscribe \$$amount',
        style: ThemeMode.light,
        merchantDisplayName: 'Flutter Stripe Store Demo',
        customerId: customerId,
        setupIntentClientSecret: paymentIntentClientSecret,
      ),
    );

    await Stripe.instance.presentPaymentSheet();
  }

  // +++++++++++++++++++++++++++++++++
  // ++ GET CUSTOMER PAYMENT METHOD ++
  // +++++++++++++++++++++++++++++++++

  Future<Map<String, dynamic>> getCustomerPaymentMethods(
    String customerId,
  ) async {
    final customerPaymentMethodsResponse = await apiService(
      endpoint: 'customers/$customerId/payment_methods',
      requestMethod: ApiServiceMethodType.get,
    );

    return customerPaymentMethodsResponse!;
  }

  // +++++++++++++++++++++++++
  // ++ CREATE SUBSCRIPTION ++
  // +++++++++++++++++++++++++

  Future<void> createSubscription({
    required String customerId,
    required String paymentId,
    required String planID,
    required String subscriptionPlan,
    required String planDescription,
    String? tempUserId, // Add tempUserId parameter
    String? newStripeCustomerID, // Add newStripeCustomerID parameter
  }) async {
    isCreatingSubscription.value = true;
    Get.back();

    try {
      final subscriptionCreationResponse = await http.post(
        Uri.parse('$baseUrl/subscriptions'),
        headers: requestHeaders,
        body: {
          'customer': customerId,
          'items[0][price]': planID,
          'default_payment_method': paymentId,
        },
      );

      // Decode the response body
      Map<String, dynamic> responseBody =
          json.decode(subscriptionCreationResponse.body);

      // Extract subscription item ID
      String subscriptionItemId = responseBody['items']['data'][0]['id'];

      // Extract subscription ID
      String subscriptionId = responseBody['id'];

      if (subscriptionCreationResponse.statusCode == 200) {
        Get.offAll(const BNB());
        getSuccessSnackBar('Subscribed successfully');
        await authController.updateSubscriptionEndsAt(
          subscriptionEndsAt: null,
        );
        await authController.updateUserSubscription(
            isPremiumUser: true,
            noOfForwardsPerMonth: subscriptionPlan == 'Lite Plan'
                ? 750
                : subscriptionPlan == 'Pro Plan'
                    ? 7500
                    : subscriptionPlan == 'Super Plan'
                        ? 65000
                        : 150000,
            subsriptionModel: SubsriptionModel(
                subscriptionItem: subscriptionItemId,
                subscriptionID: subscriptionId,
                subscriptionPlan: subscriptionPlan,
                planDescription: planDescription),
            tempUserId: tempUserId,
            newStripeCustomerID: newStripeCustomerID);
      } else {
        print(subscriptionCreationResponse.statusCode);
        print('createSubscription failed');
        getErrorSnackBar('Something went wrong, Please try again');
      }
    } catch (e) {
      getErrorSnackBar('Error: ${e.toString()}');
    } finally {
      isCreatingSubscription.value = false;
    }
  }

  Future<void> cancelSubscription(
    String subscriptionId,
  ) async {
    isCancellingSubscription.value = true;
    Get.back();

    try {
      final cancelSubcriptionResponse = await http.post(
        Uri.parse('$baseUrl/subscriptions/$subscriptionId'),
        headers: requestHeaders,
        body: {
          'cancel_at_period_end': 'true',
          'proration_behavior': 'create_prorations',
        },
      );

      if (cancelSubcriptionResponse.statusCode == 200) {
        final responseData = json.decode(cancelSubcriptionResponse.body);

        // Assuming Stripe-style API response
        final int? endTimestamp = responseData['current_period_end'];

        DateTime? subscriptionEndDate;
        if (endTimestamp != null) {
          subscriptionEndDate = DateTime.fromMillisecondsSinceEpoch(
              endTimestamp * 1000); // Stripe gives seconds
        }

        authController.updateSubscriptionEndsAt(
          subscriptionEndsAt: subscriptionEndDate,
        );

        Get.offAll(const BNB());
        getSuccessSnackBar('Subscription cancelled successfully');
      } else {
        getErrorSnackBar('Something went wrong, Please try again');
      }
    } catch (e) {
      getErrorSnackBar('Error: ${e.toString()}');
    } finally {
      isCancellingSubscription.value = false;
    }
  }

  Future<void> modifySubscription({
    required String subscriptionId,
    required String subscriptionItem,
    required String newPlanId,
    required String subscriptionPlan,
    required String planDescription,
  }) async {
    isModifyingSubscription.value = true;
    Get.back();

    try {
      await http.post(
        Uri.parse('$baseUrl/subscriptions/$subscriptionId'),
        headers: requestHeaders,
        body: {
          'cancel_at_period_end': "false",
          'proration_behavior': 'create_prorations',
          'items[0][id]': subscriptionItem,
          'items[0][price]': newPlanId, // Replace with the new plan ID
        },
      );
      Get.offAll(const BNB());
      getSuccessSnackBar('Subscription updated successfully');
      await authController.updateSubscriptionEndsAt(
        subscriptionEndsAt: null,
      );

      await authController.updateUserSubscription(
          isPremiumUser: true,
          noOfForwardsPerMonth: subscriptionPlan == 'Lite Plan'
              ? 750
              : subscriptionPlan == 'Pro Plan'
                  ? 7500
                  : subscriptionPlan == 'Super Plan'
                      ? 65000
                      : 150000,
          subsriptionModel: SubsriptionModel(
              subscriptionItem: subscriptionItem,
              subscriptionID: subscriptionId,
              subscriptionPlan: subscriptionPlan,
              planDescription: planDescription));
    } catch (e) {
      getErrorSnackBar(
          'Failed to update subscription. Please try again later.');
    } finally {
      isModifyingSubscription.value = false;
    }
  }
}
